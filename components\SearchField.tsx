"use client";
import React, {
  useState,
  useEffect,
  FormEvent,
  useCallback,
  useRef,
  KeyboardEvent,
  Suspense,
} from "react";
import { CiSearch } from "react-icons/ci";
import { motion } from "framer-motion";
import { useRouter, useSearchParams } from "next/navigation";
import useDebounce from "@/hooks/useDebounce";
import SearchSuggestions from "./SearchSuggestions";
import {
  getSearchSuggestions,
  SearchTermSuggestion,
} from "@/utils/searchUtils";

interface SearchFieldProps {
  fullWidth?: boolean;
  placeholder?: string;
  onSearch?: (query: string) => void;
  initialQuery?: string;
  debounceTime?: number;
  autoSubmit?: boolean;
  products?: ProductData[];
  showSuggestions?: boolean;
  maxSuggestions?: number;
  onSelectSuggestion?: (product: ProductData) => void;
}

// Loading component for SearchField
const SearchFieldLoading = ({
  fullWidth = false,
  placeholder = "Search products...",
}: {
  fullWidth?: boolean;
  placeholder?: string;
}) => {
  return (
    <div className={`relative ${fullWidth ? "w-full" : "w-auto"}`}>
      <div className="w-full">
        <div
          className={`flex flex-row justify-between items-center rounded-full border border-gray-200 bg-gray-50 transition-all duration-300 py-2 px-3 ${
            fullWidth ? "w-full" : ""
          }`}
        >
          <div className="bg-transparent border-none outline-none w-full text-sm text-gray-400">
            {placeholder}
          </div>
          <div className="text-gray-500 bg-transparent cursor-pointer p-1 rounded-full flex-shrink-0">
            <CiSearch size={18} />
          </div>
        </div>
      </div>
    </div>
  );
};

const SearchField = ({
  fullWidth = false,
  placeholder = "Search products...",
  onSearch,
  initialQuery = "",
  debounceTime = 1000,
  autoSubmit = false,
  products = [],
  showSuggestions = true,
  maxSuggestions = 5,
  onSelectSuggestion,
}: SearchFieldProps) => {
  return (
    <Suspense
      fallback={
        <SearchFieldLoading fullWidth={fullWidth} placeholder={placeholder} />
      }
    >
      <SearchFieldContent
        fullWidth={fullWidth}
        placeholder={placeholder}
        onSearch={onSearch}
        initialQuery={initialQuery}
        debounceTime={debounceTime}
        autoSubmit={autoSubmit}
        products={products}
        showSuggestions={showSuggestions}
        maxSuggestions={maxSuggestions}
        onSelectSuggestion={onSelectSuggestion}
      />
    </Suspense>
  );
};

const SearchFieldContent = ({
  fullWidth = false,
  placeholder = "Search products...",
  onSearch,
  initialQuery = "",
  debounceTime = 1000,
  autoSubmit = false,
  products = [],
  showSuggestions = true,
  maxSuggestions = 5,
  onSelectSuggestion,
}: SearchFieldProps) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isFocused, setIsFocused] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [searchQuery, setSearchQuery] = useState(initialQuery);
  const [productSuggestions, setProductSuggestions] = useState<ProductData[]>(
    []
  );
  const [termSuggestions, setTermSuggestions] = useState<
    SearchTermSuggestion[]
  >([]);
  const [showSuggestionsDropdown, setShowSuggestionsDropdown] = useState(false);
  const [highlightedIndex, setHighlightedIndex] = useState(-1);
  const [highlightedSection, setHighlightedSection] = useState<
    "terms" | "products"
  >("terms");
  const inputRef = useRef<HTMLInputElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Debounce the search query
  const debouncedSearchQuery = useDebounce(searchQuery, debounceTime);

  useEffect(() => {
    // Check if we're on the client side
    if (typeof window !== "undefined") {
      // Initial check
      setIsMobile(window.innerWidth < 640);

      // Add resize listener
      const handleResize = () => {
        setIsMobile(window.innerWidth < 640);
      };

      window.addEventListener("resize", handleResize);

      // Cleanup
      return () => {
        window.removeEventListener("resize", handleResize);
      };
    }
  }, []);

  // Update search query when URL changes
  useEffect(() => {
    const query = searchParams.get("query") || "";
    setSearchQuery(query);
  }, [searchParams]);

  // Handle click outside to close suggestions (like Google)
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        containerRef.current &&
        !containerRef.current.contains(event.target as Node)
      ) {
        setShowSuggestionsDropdown(false);
        setIsFocused(false);
      }
    };

    if (showSuggestionsDropdown) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showSuggestionsDropdown]);

  // Update suggestions when debounced query changes
  useEffect(() => {
    if (showSuggestions && products.length > 0) {
      if (debouncedSearchQuery.trim() !== "") {
        // Get combined suggestions (terms and products)
        const suggestions = getSearchSuggestions(
          products,
          debouncedSearchQuery,
          3, // maxTermSuggestions
          maxSuggestions
        );

        setTermSuggestions(suggestions.terms);
        setProductSuggestions(suggestions.products);

        const hasSuggestions =
          suggestions.terms.length > 0 || suggestions.products.length > 0;

        // Show suggestions if the input is focused and we have suggestions
        if (hasSuggestions && isFocused) {
          setShowSuggestionsDropdown(true);
        }
      } else if (isFocused) {
        // When search is empty but field is focused, show popular/recent suggestions
        // Get some popular products as suggestions
        const popularProducts = products.slice(0, maxSuggestions);
        setProductSuggestions(popularProducts);
        setTermSuggestions([]);

        // Show dropdown with popular suggestions when focused and empty
        if (popularProducts.length > 0) {
          setShowSuggestionsDropdown(true);
        }
      } else {
        // Clear suggestions when not focused and empty
        setTermSuggestions([]);
        setProductSuggestions([]);
        setShowSuggestionsDropdown(false);
      }
    }

    // Reset highlighted index when suggestions change
    setHighlightedIndex(-1);
    setHighlightedSection("terms");
  }, [
    debouncedSearchQuery,
    products,
    showSuggestions,
    maxSuggestions,
    isFocused,
  ]);

  // Define handleSearch callback
  const handleSearch = useCallback(
    (e?: FormEvent) => {
      if (e) e.preventDefault();

      // Close suggestions dropdown
      setShowSuggestionsDropdown(false);

      if (onSearch) {
        onSearch(searchQuery);
      } else {
        // Default behavior: navigate to products page with search query
        const currentCategory = searchParams.get("category") || "";
        const url = `/products?query=${encodeURIComponent(searchQuery)}${
          currentCategory ? `&category=${currentCategory}` : ""
        }`;
        router.push(url);
      }
    },
    [searchQuery, onSearch, router, searchParams]
  );

  // Handle product suggestion selection
  const handleSelectProduct = useCallback(
    (product: ProductData) => {
      if (onSelectSuggestion) {
        onSelectSuggestion(product);
      } else {
        // Default behavior: navigate to product page
        router.push(`/products/${product.id}`);
      }
      setShowSuggestionsDropdown(false);
    },
    [onSelectSuggestion, router]
  );

  // Handle term suggestion selection
  const handleSelectTerm = useCallback(
    (term: string) => {
      setSearchQuery(term);

      // Trigger search with the selected term
      if (onSearch) {
        onSearch(term);
      } else {
        // Default behavior: navigate to products page with search query
        const currentCategory = searchParams.get("category") || "";
        const url = `/products?query=${encodeURIComponent(term)}${
          currentCategory ? `&category=${currentCategory}` : ""
        }`;
        router.push(url);
      }

      setShowSuggestionsDropdown(false);
    },
    [onSearch, router, searchParams]
  );

  // Handle keyboard navigation
  const handleKeyDown = useCallback(
    (e: KeyboardEvent<HTMLInputElement>) => {
      if (!showSuggestionsDropdown) return;

      const termCount = termSuggestions.length;
      const productCount = productSuggestions.length;

      if (termCount === 0 && productCount === 0) return;

      // Arrow down
      if (e.key === "ArrowDown") {
        e.preventDefault();

        if (highlightedSection === "terms") {
          if (highlightedIndex < termCount - 1) {
            // Move down within terms
            setHighlightedIndex(highlightedIndex + 1);
          } else if (productCount > 0) {
            // Move to products section
            setHighlightedSection("products");
            setHighlightedIndex(0);
          } else {
            // Wrap to beginning of terms
            setHighlightedIndex(0);
          }
        } else {
          // products section
          if (highlightedIndex < productCount - 1) {
            // Move down within products
            setHighlightedIndex(highlightedIndex + 1);
          } else if (termCount > 0) {
            // Wrap to beginning of terms
            setHighlightedSection("terms");
            setHighlightedIndex(0);
          } else {
            // Wrap to beginning of products
            setHighlightedIndex(0);
          }
        }
      }
      // Arrow up
      else if (e.key === "ArrowUp") {
        e.preventDefault();

        if (highlightedSection === "terms") {
          if (highlightedIndex > 0) {
            // Move up within terms
            setHighlightedIndex(highlightedIndex - 1);
          } else if (productCount > 0) {
            // Move to end of products
            setHighlightedSection("products");
            setHighlightedIndex(productCount - 1);
          } else {
            // Wrap to end of terms
            setHighlightedIndex(termCount - 1);
          }
        } else {
          // products section
          if (highlightedIndex > 0) {
            // Move up within products
            setHighlightedIndex(highlightedIndex - 1);
          } else if (termCount > 0) {
            // Move to end of terms
            setHighlightedSection("terms");
            setHighlightedIndex(termCount - 1);
          } else {
            // Wrap to end of products
            setHighlightedIndex(productCount - 1);
          }
        }
      }
      // Enter
      else if (e.key === "Enter" && highlightedIndex >= 0) {
        e.preventDefault();

        if (highlightedSection === "terms" && termCount > 0) {
          handleSelectTerm(termSuggestions[highlightedIndex].term);
        } else if (highlightedSection === "products" && productCount > 0) {
          handleSelectProduct(productSuggestions[highlightedIndex]);
        }
      }
      // Escape
      else if (e.key === "Escape") {
        e.preventDefault();
        setShowSuggestionsDropdown(false);
      }
    },
    [
      showSuggestionsDropdown,
      termSuggestions,
      productSuggestions,
      highlightedIndex,
      highlightedSection,
      handleSelectProduct,
      handleSelectTerm,
    ]
  );

  // Auto-submit search when debounced query changes (if autoSubmit is enabled)
  useEffect(() => {
    if (
      autoSubmit &&
      debouncedSearchQuery !== initialQuery &&
      debouncedSearchQuery.trim() !== ""
    ) {
      handleSearch();
    }
  }, [debouncedSearchQuery, autoSubmit, handleSearch, initialQuery]);

  return (
    <div ref={containerRef} className={`relative ${fullWidth ? "w-full" : "w-auto"}`}>
      <form onSubmit={handleSearch} className="w-full">
        <motion.div
          className={`flex flex-row justify-between items-center rounded-full border ${
            isFocused
              ? "border-accent bg-white shadow-md"
              : "border-gray-200 bg-gray-50"
          } transition-all duration-300 py-2 px-3 ${fullWidth ? "w-full" : ""}`}
          animate={
            !fullWidth
              ? {
                  width: isFocused
                    ? isMobile
                      ? "160px"
                      : "220px"
                    : isMobile
                    ? "140px"
                    : "180px",
                }
              : {}
          }
          transition={{ type: "spring", stiffness: 300, damping: 25 }}
        >
          <input
            ref={inputRef}
            type="text"
            name="search-field"
            id="search-field"
            value={searchQuery}
            onChange={(e) => {
              const newValue = e.target.value;
              setSearchQuery(newValue);

              // Keep the suggestions dropdown open while typing if there's text
              if (newValue.trim() !== "" && isFocused) {
                setShowSuggestionsDropdown(true);
              } else {
                setShowSuggestionsDropdown(false);
              }
            }}
            onKeyDown={handleKeyDown}
            className="bg-transparent border-none outline-none w-full text-sm"
            placeholder={isMobile && !fullWidth ? "Search..." : placeholder}
            onFocus={() => {
              setIsFocused(true);

              // Show suggestions immediately when focused (like Google)
              if (showSuggestions) {
                // If there's text, show relevant suggestions
                if (searchQuery.trim() !== "") {
                  setShowSuggestionsDropdown(true);
                } else {
                  // If empty, show popular products as suggestions
                  if (products.length > 0) {
                    const popularProducts = products.slice(0, maxSuggestions);
                    setProductSuggestions(popularProducts);
                    setTermSuggestions([]);
                    setShowSuggestionsDropdown(true);
                  }
                }
              }
            }}
            onBlur={() => {
              // Don't hide suggestions on blur - let click outside handle it
              // This allows users to interact with suggestions without them disappearing
            }}
          />
          <motion.button
            type="submit"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            transition={{ type: "spring", stiffness: 400, damping: 10 }}
            className={`${
              isFocused
                ? "text-accent bg-accent/10"
                : "text-gray-500 bg-transparent"
            } cursor-pointer p-1 rounded-full flex-shrink-0`}
          >
            <CiSearch size={18} />
          </motion.button>
        </motion.div>
      </form>

      {/* Search Suggestions Dropdown */}
      {showSuggestions && (
        <SearchSuggestions
          termSuggestions={termSuggestions}
          productSuggestions={productSuggestions}
          isVisible={showSuggestionsDropdown}
          onSelectProduct={handleSelectProduct}
          onSelectTerm={handleSelectTerm}
          onClose={() => setShowSuggestionsDropdown(false)}
          highlightedIndex={highlightedIndex}
          highlightedSection={highlightedSection}
          setHighlightedIndex={(index, section) => {
            setHighlightedIndex(index);
            setHighlightedSection(section);
          }}
          maxProductSuggestions={maxSuggestions}
          searchQuery={searchQuery}
        />
      )}
    </div>
  );
};

export default SearchField;

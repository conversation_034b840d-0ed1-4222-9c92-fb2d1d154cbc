# How to Add YouTube Videos to the Blogs Page

This guide explains how to add YouTube videos to your blogs page.

## Quick Method

You can use the `addQuickYouTubeBlog` function to easily add new YouTube videos. Here's how:

### 1. Open the Browser Console

1. Go to your website's blogs page (`/blogs`)
2. Open the browser developer tools (F12)
3. Go to the Console tab

### 2. Import the Function

In the console, type:

```javascript
import { addQuickYouTubeBlog } from '/apis/youtubeBlogs.js';
```

### 3. Add a YouTube Video

Use this format to add a video:

```javascript
addQuickYouTubeBlog(
  "YOUTUBE_URL_HERE",
  "Video Title",
  "Video Description",
  "Category",
  ["tag1", "tag2", "tag3"]
);
```

### Example:

```javascript
addQuickYouTubeBlog(
  "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
  "Traditional Chinioti Wood Carving Techniques",
  "Watch master craftsmen demonstrate the ancient art of Chinioti wood carving, passed down through generations.",
  "Craftsmanship",
  ["woodworking", "traditional", "carving", "chinioti"]
);
```

## Manual Method

If you prefer to edit the code directly:

### 1. Open the YouTube Blogs API File

Navigate to `apis/youtubeBlogs.ts`

### 2. Find the Sample Data

Look for the `sampleYouTubeBlogs` array around line 40.

### 3. Add Your Video

Add a new object to the array:

```typescript
{
  id: "4", // Use the next available number
  title: "Your Video Title",
  description: "Your video description here...",
  youtubeUrl: "https://www.youtube.com/watch?v=YOUR_VIDEO_ID",
  youtubeVideoId: "YOUR_VIDEO_ID", // Extract from the URL
  thumbnailUrl: "https://img.youtube.com/vi/YOUR_VIDEO_ID/maxresdefault.jpg",
  category: "Your Category",
  tags: ["tag1", "tag2", "tag3"],
  isActive: true,
  views: 0,
  createdAt: "2024-01-20T10:00:00Z", // Use current date
  updatedAt: "2024-01-20T10:00:00Z",
  author: {
    id: "1",
    name: "Your Name",
    email: "<EMAIL>"
  }
}
```

## Video Categories

You can use these categories or create new ones:
- Craftsmanship
- Process
- Education
- Care Tips
- Design
- History
- Sustainability

## Getting YouTube Video Information

### Video ID
From a URL like `https://www.youtube.com/watch?v=dQw4w9WgXcQ`, the video ID is `dQw4w9WgXcQ`

### Thumbnail URL
YouTube automatically generates thumbnails. Use this format:
`https://img.youtube.com/vi/VIDEO_ID/maxresdefault.jpg`

### Supported URL Formats
- `https://www.youtube.com/watch?v=VIDEO_ID`
- `https://youtu.be/VIDEO_ID`
- `https://www.youtube.com/embed/VIDEO_ID`

## Tips

1. **Video Quality**: Use high-quality videos that represent your brand well
2. **Descriptions**: Write detailed descriptions to help with SEO
3. **Tags**: Use relevant tags to help users find your content
4. **Categories**: Keep categories consistent for better organization
5. **Thumbnails**: YouTube's auto-generated thumbnails work well, but you can also use custom ones

## Testing

After adding videos:
1. Refresh the blogs page
2. Check that the video appears in the grid
3. Click on the video to test the detail page
4. Verify the YouTube embed works correctly

## Future Enhancements

In a production environment, you would:
- Connect to a real database
- Add an admin panel for easier video management
- Implement user authentication for admin functions
- Add video analytics and view tracking
- Enable comments and ratings

## Troubleshooting

**Video not showing?**
- Check the YouTube URL is correct
- Ensure the video is public
- Verify the video ID is extracted correctly

**Thumbnail not loading?**
- YouTube thumbnails may take time to generate
- Try using a different quality: `default`, `medium`, `high`, or `maxres`

**Console errors?**
- Check the browser console for JavaScript errors
- Ensure all imports are correct
- Verify the video data format matches the interface

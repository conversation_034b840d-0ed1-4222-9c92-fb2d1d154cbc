'use client';
import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { Heart, ShoppingCart, Share2, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useCart } from '@/contexts/CartContext';
import { useModal } from '@/contexts/ModalContext';
import { useCurrency } from '@/contexts/CurrencyContext';
import { toast } from 'sonner';
import WhatsAppOrderButton from '@/components/WhatsAppOrderButton';

// Define ProductData interface locally to fix import issue
interface ProductData {
  id: string;
  type: string;
  video?: string;
  images: string[];
  image?: string;
  title: string;
  category: string;
  available: boolean;
  discount: string;
  price: number;
  description?: string;
  quantity?: number;
  created?: string;
  name?: string;
}

interface ProductDetailModalProps {
  product: ProductData;
  onClose: () => void;
}

const ProductDetailModal: React.FC<ProductDetailModalProps> = ({
  product,
  onClose,
}) => {
  const [selectedImage, setSelectedImage] = useState<string>(product.image || '');
  const [quantity, setQuantity] = useState(1);
  const [showQuickOrderForm, setShowQuickOrderForm] = useState(false);
  const [quickOrderData, setQuickOrderData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    postalCode: '',
  });
  const { addToCart } = useCart();
  const { openModal, closeModal } = useModal();
  const { formatPrice } = useCurrency();

  const modalId = 'product-detail-modal';

  // Prevent background scrolling when modal is open and register with modal context
  useEffect(() => {
    // Register modal as open
    openModal(modalId);

    // Save current body overflow style
    const originalStyle = window.getComputedStyle(document.body).overflow;

    // Prevent scrolling
    document.body.style.overflow = 'hidden';

    // Cleanup function to restore original overflow and unregister modal
    return () => {
      document.body.style.overflow = originalStyle;
      closeModal(modalId);
    };
  }, [openModal, closeModal, modalId]);

  // Calculate discounted price if available
  const discountedPrice = product.discount
    ? Math.round(
        product.price - product.price * (parseInt(product.discount) / 100)
      )
    : null;

  const handleAddToCart = () => {
    addToCart(product, quantity);
  };

  const handleQuantityChange = (value: number) => {
    if (value >= 1) {
      setQuantity(value);
    }
  };

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const handleQuickOrderInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setQuickOrderData(prev => ({ ...prev, [name]: value }));
  };

  const handleQuickWhatsAppOrder = () => {
    // Validate required fields
    const requiredFields = ['firstName', 'lastName', 'phone', 'address', 'city'];
    const missingFields = requiredFields.filter(field => !quickOrderData[field as keyof typeof quickOrderData]);

    if (missingFields.length > 0) {
      toast.error('Please fill in all required fields');
      return null;
    }

    // Create a cart item with this product
    const cartItem = {
      product,
      quantity,
    };

    const cart = {
      items: [cartItem],
      totalItems: quantity,
      subtotal: product.price * quantity,
      discount: product.discount ? (product.price * quantity * parseInt(product.discount)) / 100 : 0,
      total: product.price * quantity - (product.discount ? (product.price * quantity * parseInt(product.discount)) / 100 : 0),
    };

    // Return the order data
    return {
      cart,
      customerDetails: quickOrderData,
    };
  };

  function isInWishlist(id: string) {
    const wishlist = localStorage.getItem('wishlist');
    return wishlist ? JSON.parse(wishlist).includes(id) : false;
  }

  function removeFromWishlist(id: string) {
    const wishlist = localStorage.getItem('wishlist');
    const wishlistArray = wishlist ? JSON.parse(wishlist) : [];
    const updatedWishlist = wishlistArray.filter((item: string) => item !== id);
    localStorage.setItem('wishlist', JSON.stringify(updatedWishlist));
    toast.success('Removed from wishlist');
  }

  function addToWishlist(id: string) {
    const wishlist = localStorage.getItem('wishlist');
    const wishlistArray = wishlist ? JSON.parse(wishlist) : [];
    wishlistArray.push(id);
    localStorage.setItem('wishlist', JSON.stringify(wishlistArray));
    toast.success('Added to wishlist');
  }

  return (
    <div
      className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-2 sm:p-4 overflow-y-auto"
      onClick={handleBackdropClick}
    >
      <div className="bg-white rounded-lg shadow-xl w-full max-w-6xl max-h-[95vh] sm:max-h-[90vh] my-2 sm:my-4 overflow-hidden flex flex-col">
        {/* Close button */}
        <button
          onClick={onClose}
          className="absolute top-2 right-2 sm:top-4 sm:right-4 p-1.5 sm:p-2 bg-white rounded-full shadow-md z-50 hover:bg-gray-100"
        >
          <X size={18} className="sm:w-5 sm:h-5 text-gray-700" />
        </button>

        <div className="flex flex-col md:flex-row flex-1 min-h-0 overflow-hidden">
          {/* Left side - Product images */}
          <div className="w-full md:w-1/2 p-4 sm:p-6 bg-gray-50 overflow-y-auto">
            <div className="relative aspect-square overflow-hidden rounded-lg bg-white">
              <Image
                src={selectedImage}
                alt={product.title}
                fill
                className="object-contain"
                priority
              />
            </div>
            <div className="flex gap-2 mt-4 overflow-x-auto pb-2">
              {product.images.map((img: string, idx: number) => (
                <div
                  key={idx}
                  className={`relative w-20 h-20 rounded-md overflow-hidden cursor-pointer border-2 ${
                    selectedImage === img
                      ? 'border-accent'
                      : 'border-transparent'
                  }`}
                  onClick={() => setSelectedImage(img)}
                >
                  <Image
                    src={img}
                    alt={`${product.title} - image ${idx + 1}`}
                    fill
                    className="object-cover"
                  />
                </div>
              ))}
            </div>
          </div>

          {/* Right side - Product info */}
          <div className="w-full md:w-1/2 p-4 sm:p-6 overflow-y-auto flex-1 min-h-0">
            <div className="text-sm sm:text-md text-gray-500 mb-1">
              {product.category}
            </div>
            <h1 className="text-xl sm:text-2xl md:text-3xl font-bold text-gray-900 mb-2">
              {product.title}
            </h1>

            {/* Price */}
            <div className="flex flex-wrap items-center gap-2 mb-4">
              {discountedPrice ? (
                <>
                  <span className="text-xl sm:text-2xl font-bold text-accent">
                    {formatPrice(discountedPrice)}
                  </span>
                  <span className="text-base sm:text-lg text-gray-400 line-through">
                    {formatPrice(product.price)}
                  </span>
                  <span className="px-2 py-1 bg-accent/10 text-accent text-xs sm:text-sm font-medium rounded">
                    {product.discount}% OFF
                  </span>
                </>
              ) : (
                <span className="text-xl sm:text-2xl font-bold text-gray-900">
                  {formatPrice(product.price)}
                </span>
              )}
            </div>

            {/* Availability */}
            <div className="mb-6">
              <span
                className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${
                  product.available
                    ? 'bg-green-100 text-green-800'
                    : 'bg-red-100 text-red-800'
                }`}
              >
                {product.available ? 'IN STOCK' : 'OUT OF STOCK'}
              </span>
            </div>

            {/* Description */}
            <div className="prose prose-sm max-w-none text-gray-600 mb-6 sm:mb-8">
              <p>
                EXPERIENCE THE EXQUISITE CRAFTSMANSHIP OF CHINIOTI WOODEN ART
              </p>
            </div>

            {/* Quantity Selector */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Quantity
              </label>
              <div className="flex items-center border border-gray-300 rounded-md w-fit">
                <button
                  type="button"
                  className="px-3 py-1 text-gray-600 hover:text-accent"
                  onClick={() => handleQuantityChange(quantity - 1)}
                >
                  -
                </button>
                <span className="px-4 py-1 text-center min-w-[3rem]">{quantity}</span>
                <button
                  type="button"
                  className="px-3 py-1 text-gray-600 hover:text-accent"
                  onClick={() => handleQuantityChange(quantity + 1)}
                >
                  +
                </button>
              </div>
            </div>
            {/* Action buttons */}
            <div className="flex flex-wrap gap-2 sm:gap-4">
              <button
                className="flex items-center gap-2 bg-[#e37100] rounded-md cursor-pointer  text-white px-4 sm:px-6 py-2 text-sm sm:text-base"
                onClick={handleAddToCart}
                disabled={!product.available}
              >
                <ShoppingCart size={16} className="sm:w-[18px] sm:h-[18px]" />
                <span>ADD TO CART</span>
              </button>

              <button
                onClick={() => {
                  if (isInWishlist(product.id)) {
                    removeFromWishlist(product.id);
                  } else {
                    addToWishlist(product.id);
                  }
                }}
                className="flex items-center gap-2 border-gray-300  bg-[#e37100] px-4 sm:px-6 py-2 text-sm sm:text-base text-white rounded-md cursor-pointer"
              >
                <Heart size={16} className="sm:w-[18px] sm:h-[18px]" />
                <span className="hidden sm:inline ">ADD TO WISHLIST</span>
                <span className="sm:hidden">WISHLIST</span>
              </button>

              <button
                onClick={() => setShowQuickOrderForm(!showQuickOrderForm)}
                className="flex items-center gap-2 border border-gray-300 bg-white px-4 sm:px-6 py-2 text-sm sm:text-base text-gray-700 rounded-md cursor-pointer hover:bg-gray-50"
              >
                <span className="hidden sm:inline">Order via WhatsApp</span>
                <span className="sm:hidden">WhatsApp</span>
              </button>
            </div>

            {/* Quick Order Form */}
            {showQuickOrderForm && (
              <div className="mt-4 p-4 border border-gray-200 rounded-lg bg-gray-50">
                <h4 className="text-sm font-semibold mb-3">Quick Order via WhatsApp</h4>
                <div className="space-y-3">
                  <div className="grid grid-cols-2 gap-2">
                    <input
                      type="text"
                      name="firstName"
                      placeholder="First Name *"
                      value={quickOrderData.firstName}
                      onChange={handleQuickOrderInputChange}
                      className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-accent/50"
                      required
                    />
                    <input
                      type="text"
                      name="lastName"
                      placeholder="Last Name *"
                      value={quickOrderData.lastName}
                      onChange={handleQuickOrderInputChange}
                      className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-accent/50"
                      required
                    />
                  </div>
                  
                  <input
                    type="email"
                    name="email"
                    placeholder="Email Address"
                    value={quickOrderData.email}
                    onChange={handleQuickOrderInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-accent/50"
                  />
                  
                  <input
                    type="tel"
                    name="phone"
                    placeholder="Phone Number *"
                    value={quickOrderData.phone}
                    onChange={handleQuickOrderInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-accent/50"
                    required
                  />
                  
                  <textarea
                    name="address"
                    placeholder="Delivery Address *"
                    value={quickOrderData.address}
                    onChange={handleQuickOrderInputChange}
                    rows={2}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-accent/50"
                    required
                  />
                  
                  <div className="grid grid-cols-2 gap-2">
                    <input
                      type="text"
                      name="city"
                      placeholder="City *"
                      value={quickOrderData.city}
                      onChange={handleQuickOrderInputChange}
                      className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-accent/50"
                      required
                    />
                    <input
                      type="text"
                      name="postalCode"
                      placeholder="Postal Code"
                      value={quickOrderData.postalCode}
                      onChange={handleQuickOrderInputChange}
                      className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-accent/50"
                    />
                  </div>

                  <WhatsAppOrderButton
                    orderData={handleQuickWhatsAppOrder() || {
                      cart: {
                        items: [{ product, quantity }],
                        totalItems: quantity,
                        subtotal: product.price * quantity,
                        discount: 0,
                        total: product.price * quantity,
                      },
                      customerDetails: quickOrderData,
                    }}
                    className="w-full"
                    size="sm"
                    disabled={!quickOrderData.firstName || !quickOrderData.lastName || !quickOrderData.phone || !quickOrderData.address || !quickOrderData.city}
                  >
                    Send Order via WhatsApp
                  </WhatsAppOrderButton>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductDetailModal;

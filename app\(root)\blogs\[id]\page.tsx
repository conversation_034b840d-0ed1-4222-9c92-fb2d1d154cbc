"use client";
import React, { useState, useEffect, useRef } from "react";
import { motion } from "framer-motion";
import Link from "next/link";
import { useParams, useRouter } from "next/navigation";
import { ArrowLeft, Calendar, User, Eye, Share2, ExternalLink, Tag, SkipForward, SkipBack, Play, Pause } from "lucide-react";
import { Button } from "@/components/ui/button";
import { getYouTubeBlogById, getYouTubeEmbedUrl, getYouTubeBlogs, YouTubeBlogData } from "@/apis/youtubeBlogs";
import { toast } from "sonner";

// YouTube API type declarations
declare global {
  interface Window {
    YT: any;
    onYouTubeIframeAPIReady: () => void;
  }
}

const BlogDetail = () => {
  const params = useParams();
  const router = useRouter();
  const blogId = params.id as string;
  const [blog, setBlog] = useState<YouTubeBlogData | null>(null);
  const [allBlogs, setAllBlogs] = useState<YouTubeBlogData[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [isAutoPlay, setIsAutoPlay] = useState(true);
  const iframeRef = useRef<HTMLIFrameElement>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch all blogs for playlist functionality
        const blogsResult = await getYouTubeBlogs(1, 100); // Get all blogs
        const blogs = blogsResult.blogs;
        setAllBlogs(blogs);

        // Find current blog and its index
        const currentBlogIndex = blogs.findIndex(b => b.id === blogId);
        if (currentBlogIndex !== -1) {
          setBlog(blogs[currentBlogIndex]);
          setCurrentIndex(currentBlogIndex);
        } else {
          // Fallback: try to fetch individual blog
          const fetchedBlog = await getYouTubeBlogById(blogId);
          if (fetchedBlog) {
            setBlog(fetchedBlog);
            setCurrentIndex(0);
          } else {
            toast.error("Blog not found");
          }
        }
      } catch (error) {
        console.error("Error fetching blog:", error);
        toast.error("Failed to load blog");
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [blogId]);

  // Auto-play navigation functions
  const goToNextVideo = () => {
    if (allBlogs.length === 0) return;

    const nextIndex = (currentIndex + 1) % allBlogs.length; // Loop back to start
    const nextBlog = allBlogs[nextIndex];

    setCurrentIndex(nextIndex);
    setBlog(nextBlog);

    // Show notification for auto-play
    if (isAutoPlay) {
      toast.success(`Auto-playing: ${nextBlog.title}`, {
        duration: 3000,
      });
    }

    router.push(`/blogs/${nextBlog.id}`);
  };

  const goToPreviousVideo = () => {
    if (allBlogs.length === 0) return;

    const prevIndex = currentIndex === 0 ? allBlogs.length - 1 : currentIndex - 1; // Loop to end
    const prevBlog = allBlogs[prevIndex];

    setCurrentIndex(prevIndex);
    setBlog(prevBlog);
    router.push(`/blogs/${prevBlog.id}`);
  };

  // YouTube iframe API integration for auto-play
  useEffect(() => {
    if (!blog || !isAutoPlay) return;

    // Load YouTube IFrame API
    const loadYouTubeAPI = () => {
      if (window.YT) {
        initializePlayer();
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://www.youtube.com/iframe_api';
      script.async = true;
      document.body.appendChild(script);

      window.onYouTubeIframeAPIReady = initializePlayer;
    };

    const initializePlayer = () => {
      if (!window.YT || !iframeRef.current) return;

      new window.YT.Player(iframeRef.current, {
        events: {
          onStateChange: (event: any) => {
            // When video ends (state 0), play next video
            if (event.data === window.YT.PlayerState.ENDED && isAutoPlay) {
              setTimeout(() => {
                goToNextVideo();
              }, 1000); // 1 second delay before next video
            }
          }
        }
      });
    };

    loadYouTubeAPI();
  }, [blog, isAutoPlay, currentIndex, allBlogs]);

  const handleShare = async () => {
    if (navigator.share && blog) {
      try {
        await navigator.share({
          title: blog.title,
          text: blog.description,
          url: window.location.href,
        });
      } catch (error) {
        // Fallback to copying URL
        navigator.clipboard.writeText(window.location.href);
        toast.success("Link copied to clipboard!");
      }
    } else {
      // Fallback to copying URL
      navigator.clipboard.writeText(window.location.href);
      toast.success("Link copied to clipboard!");
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatViews = (views: number) => {
    if (views >= 1000000) {
      return `${(views / 1000000).toFixed(1)}M`;
    } else if (views >= 1000) {
      return `${(views / 1000).toFixed(1)}K`;
    }
    return views.toString();
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-accent"></div>
      </div>
    );
  }

  if (!blog) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Blog not found</h1>
          <Link href="/blogs">
            <Button>
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Blogs
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="container mx-auto px-4 py-6">
          <Link href="/blogs">
            <Button variant="ghost" className="mb-4">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Video Blogs
            </Button>
          </Link>
        </div>
      </div>

      {/* Main Content */}
      <article className="container mx-auto px-4 py-8 max-w-4xl">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="bg-white rounded-lg shadow-lg overflow-hidden"
        >
          {/* Video Player */}
          <div className="relative">
            <div className="aspect-video bg-gray-100">
              <iframe
                ref={iframeRef}
                src={`${getYouTubeEmbedUrl(blog.youtubeVideoId)}?enablejsapi=1&autoplay=1`}
                title={blog.title}
                className="w-full h-full"
                style={{ border: 0 }}
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowFullScreen
              />
            </div>

            {/* Video Controls Overlay */}
            <div className="absolute top-4 right-4 flex items-center gap-2">
              <Button
                variant="secondary"
                size="sm"
                onClick={() => setIsAutoPlay(!isAutoPlay)}
                className="bg-black/70 text-white hover:bg-black/80"
              >
                {isAutoPlay ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                <span className="ml-1 text-xs">{isAutoPlay ? 'Auto' : 'Manual'}</span>
              </Button>
            </div>

            {/* Navigation Controls */}
            {allBlogs.length > 1 && (
              <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex items-center gap-2">
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={goToPreviousVideo}
                  className="bg-black/70 text-white hover:bg-black/80"
                >
                  <SkipBack className="w-4 h-4" />
                </Button>

                <div className="bg-black/70 text-white px-3 py-1 rounded text-sm">
                  {currentIndex + 1} / {allBlogs.length}
                </div>

                <Button
                  variant="secondary"
                  size="sm"
                  onClick={goToNextVideo}
                  className="bg-black/70 text-white hover:bg-black/80"
                >
                  <SkipForward className="w-4 h-4" />
                </Button>
              </div>
            )}
          </div>

          {/* Content */}
          <div className="p-6 md:p-8">
            {/* Header */}
            <header className="mb-6">
              {/* Category */}
              <div className="mb-3">
                <span className="bg-accent text-white text-sm font-medium px-3 py-1 rounded">
                  {blog.category}
                </span>
              </div>

              {/* Title */}
              <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                {blog.title}
              </h1>

              {/* Meta information */}
              <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600 mb-4">
                {blog.author && (
                  <div className="flex items-center gap-1">
                    <User className="w-4 h-4" />
                    <span>{blog.author.name}</span>
                  </div>
                )}
                <div className="flex items-center gap-1">
                  <Calendar className="w-4 h-4" />
                  <span>{formatDate(blog.createdAt)}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Eye className="w-4 h-4" />
                  <span>{formatViews(blog.views)} views</span>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex flex-wrap items-center gap-3">
                <Button
                  onClick={() => window.open(blog.youtubeUrl, '_blank')}
                  className="flex items-center gap-2"
                >
                  <ExternalLink className="w-4 h-4" />
                  Watch on YouTube
                </Button>

                <Button variant="outline" onClick={handleShare}>
                  <Share2 className="w-4 h-4 mr-2" />
                  Share Video
                </Button>
              </div>
            </header>

            {/* Description */}
            <div className="prose prose-lg max-w-none mb-6">
              <p className="text-gray-700 leading-relaxed">
                {blog.description}
              </p>
            </div>

            {/* Tags */}
            {blog.tags && blog.tags.length > 0 && (
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Tags</h3>
                <div className="flex flex-wrap gap-2">
                  {blog.tags.map((tag, index) => (
                    <span
                      key={index}
                      className="inline-flex items-center gap-1 text-sm bg-gray-100 text-gray-700 px-3 py-1 rounded-full"
                    >
                      <Tag className="w-3 h-3" />
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {/* Playlist Section */}
            {allBlogs.length > 1 && (
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  Up Next ({allBlogs.length - 1} videos)
                </h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 max-h-96 overflow-y-auto">
                  {allBlogs
                    .filter((_, index) => index !== currentIndex)
                    .slice(0, 6) // Show max 6 upcoming videos
                    .map((upcomingBlog) => (
                      <div
                        key={upcomingBlog.id}
                        className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer transition-colors"
                        onClick={() => {
                          const blogIndex = allBlogs.findIndex(b => b.id === upcomingBlog.id);
                          setCurrentIndex(blogIndex);
                          setBlog(upcomingBlog);
                          router.push(`/blogs/${upcomingBlog.id}`);
                        }}
                      >
                        <div className="relative w-20 h-12 bg-gray-200 rounded overflow-hidden flex-shrink-0">
                          <img
                            src={upcomingBlog.thumbnailUrl}
                            alt={upcomingBlog.title}
                            className="w-full h-full object-cover"
                            onError={(e) => {
                              const target = e.target as HTMLImageElement;
                              target.style.display = 'none';
                            }}
                          />
                          <div className="absolute inset-0 flex items-center justify-center">
                            <Play className="w-4 h-4 text-white drop-shadow-lg" />
                          </div>
                        </div>
                        <div className="flex-1 min-w-0">
                          <h4 className="text-sm font-medium text-gray-900 line-clamp-2">
                            {upcomingBlog.title}
                          </h4>
                          <p className="text-xs text-gray-500 mt-1">
                            {upcomingBlog.category} • {formatViews(upcomingBlog.views)} views
                          </p>
                        </div>
                      </div>
                    ))}
                </div>
              </div>
            )}

            {/* Footer */}
            <footer className="pt-6 border-t border-gray-200">
              <div className="flex items-center justify-between">
                <div className="text-sm text-gray-600">
                  Published on {formatDate(blog.createdAt)}
                  {isAutoPlay && allBlogs.length > 1 && (
                    <span className="ml-2 text-accent">• Auto-play enabled</span>
                  )}
                </div>
                <Link href="/blogs">
                  <Button variant="outline">
                    View All Videos
                  </Button>
                </Link>
              </div>
            </footer>
          </div>
        </motion.div>
      </article>
    </div>
  );
};

export default BlogDetail;
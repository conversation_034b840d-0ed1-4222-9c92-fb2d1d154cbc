"use client";
import React, { useEffect, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { X, Home, ShoppingBag, Heart, User, Phone, Info } from "lucide-react";
import { secondaryFont } from "@/constants/fonts";

interface NavItem {
  name: string;
  path: string;
  icon?: React.ReactNode;
}

interface MobileSidebarProps {
  isOpen: boolean;
  onClose: () => void;
  navItems?: NavItem[];
}

const MobileSidebar: React.FC<MobileSidebarProps> = ({
  isOpen,
  onClose,
  navItems = [
    { name: "Home", path: "/", icon: <Home size={20} /> },
    { name: "About Us", path: "/about-us", icon: <Info size={20} /> },
    { name: "Contact Us", path: "/contact", icon: <Phone size={20} /> },
    { name: "Products", path: "/products", icon: <ShoppingBag size={20} /> },
    { name: "Wishlist", path: "/wishlist", icon: <Heart size={20} /> },
  ],
}) => {
  const pathname = usePathname();
  const sidebarRef = useRef<HTMLDivElement>(null);

  // Handle escape key press
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === "Escape" && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleEscape);
      // Prevent body scroll when sidebar is open
      document.body.classList.add("no-scroll");
    } else {
      document.body.classList.remove("no-scroll");
    }

    return () => {
      document.removeEventListener("keydown", handleEscape);
      document.body.classList.remove("no-scroll");
    };
  }, [isOpen, onClose]);

  // Focus management and focus trap
  useEffect(() => {
    if (isOpen && sidebarRef.current) {
      const sidebar = sidebarRef.current;
      const focusableElements = sidebar.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      ) as NodeListOf<HTMLElement>;

      const firstFocusableElement = focusableElements[0];
      const lastFocusableElement = focusableElements[focusableElements.length - 1];

      // Focus the first element when sidebar opens
      if (firstFocusableElement) {
        firstFocusableElement.focus();
      }

      // Handle tab key for focus trap
      const handleTabKey = (event: KeyboardEvent) => {
        if (event.key === "Tab") {
          if (event.shiftKey) {
            // Shift + Tab
            if (document.activeElement === firstFocusableElement) {
              event.preventDefault();
              lastFocusableElement?.focus();
            }
          } else {
            // Tab
            if (document.activeElement === lastFocusableElement) {
              event.preventDefault();
              firstFocusableElement?.focus();
            }
          }
        }
      };

      sidebar.addEventListener("keydown", handleTabKey);

      return () => {
        sidebar.removeEventListener("keydown", handleTabKey);
      };
    }
  }, [isOpen]);

  const sidebarVariants = {
    closed: {
      x: "-100%",
      transition: {
        type: "tween",
        duration: 0.3,
        ease: "easeInOut",
      },
    },
    open: {
      x: 0,
      transition: {
        type: "tween",
        duration: 0.3,
        ease: "easeInOut",
      },
    },
  };

  const overlayVariants = {
    closed: {
      opacity: 0,
      transition: {
        duration: 0.3,
      },
    },
    open: {
      opacity: 1,
      transition: {
        duration: 0.3,
      },
    },
  };

  const itemVariants = {
    closed: {
      x: -20,
      opacity: 0,
    },
    open: (i: number) => ({
      x: 0,
      opacity: 1,
      transition: {
        delay: i * 0.1,
        duration: 0.3,
        ease: "easeOut",
      },
    }),
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Overlay */}
          <motion.div
            className="fixed inset-0 bg-black/50 z-40 md:hidden"
            variants={overlayVariants}
            initial="closed"
            animate="open"
            exit="closed"
            onClick={onClose}
            aria-hidden="true"
          />

          {/* Sidebar */}
          <motion.div
            ref={sidebarRef}
            className="fixed top-0 left-0 h-full w-80 max-w-[85vw] bg-white shadow-2xl z-50 md:hidden"
            variants={sidebarVariants}
            initial="closed"
            animate="open"
            exit="closed"
            role="dialog"
            aria-modal="true"
            aria-labelledby="mobile-nav-title"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              <h2
                id="mobile-nav-title"
                className={`${secondaryFont} text-xl font-bold text-gray-800`}
              >
                <span className="text-accent">Chinioti</span>{" "}
                <span className="text-gray-800">Wooden Art</span>
              </h2>
              <button
                onClick={onClose}
                className="touch-target p-2 rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-accent focus:ring-offset-2 transition-colors"
                aria-label="Close navigation menu"
              >
                <X size={24} className="text-gray-600" />
              </button>
            </div>

            {/* Navigation Items */}
            <nav className="flex-1 px-4 py-6" role="navigation" aria-label="Main navigation">
              <ul className="space-y-2" role="menu" aria-orientation="vertical">
                {navItems.map((item, index) => {
                  const isActive = pathname === item.path;
                  
                  return (
                    <motion.li
                      key={item.path}
                      variants={itemVariants}
                      initial="closed"
                      animate="open"
                      custom={index}
                    >
                      <Link
                        href={item.path}
                        onClick={onClose}
                        onKeyDown={(e) => {
                          if (e.key === "Enter" || e.key === " ") {
                            e.preventDefault();
                            onClose();
                          }
                        }}
                        className={`
                          mobile-nav-item
                          w-full rounded-lg
                          text-left font-medium
                          transition-all duration-200
                          focus:outline-none focus:ring-2 focus:ring-accent focus:ring-offset-2
                          ${
                            isActive
                              ? "bg-accent text-white shadow-md"
                              : "text-gray-700 hover:bg-accent/10 hover:text-accent"
                          }
                        `}
                        role="menuitem"
                        tabIndex={0}
                        aria-current={isActive ? "page" : undefined}
                      >
                        <div className="flex items-center space-x-3">
                          {item.icon && (
                            <span className={isActive ? "text-white" : "text-gray-500"}>
                              {item.icon}
                            </span>
                          )}
                          <span>{item.name}</span>
                        </div>
                      </Link>
                    </motion.li>
                  );
                })}
              </ul>
            </nav>

            {/* Footer */}
            <div className="p-4 border-t border-gray-200">
              <p className="text-xs text-gray-500 text-center">
                © 2024 Chinioti Wooden Art
              </p>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};

export default MobileSidebar;

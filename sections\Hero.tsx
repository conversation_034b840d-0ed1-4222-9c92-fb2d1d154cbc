import React from "react";
import { But<PERSON> } from "../components/ui/button";
import { secondaryFont } from "@/constants/fonts";
import Link from "next/link";
import Image from "next/image";

const Hero = () => {
  return (
    <section
      className="relative flex items-center justify-center bg-cover bg-no-repeat bg-center w-full min-h-[32rem] sm:min-h-[36rem] md:h-[30rem] px-4 sm:px-5 py-8 sm:py-12 text-center"
      aria-labelledby="hero-heading"
      role="banner"
    >
      {/* Background Image with proper alt text for SEO */}
      <div className="absolute inset-0 ">
        <Image
          src="/home-background.svg"
          alt="Traditional Chinioti wooden furniture craftsman creating handcrafted furniture in Chiniot, Punjab, Pakistan workshop"
          fill
          className="object-cover"
          priority
          sizes="100vw"
        />
      </div>
      <div className="relative grid place-items-center h-full gap-6 sm:gap-8 md:gap-10 z-10">
        <header>
          <h1
            id="hero-heading"
            className={
              secondaryFont +
              " grid text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-semibold font-secondary text-white leading-tight drop-shadow-lg"
            }
          >
            Discover the World of{" "}
            <span className="text-accent">Authentic Chinioti</span>
            <br />
            <span className="text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl">
              Handcrafted Wooden Furniture
            </span>
          </h1>
          <p className="text-white text-md sm:text-base md:text-lg lg:text-xl mt-3 sm:mt-4 max-w-2xl drop-shadow-md"></p>
        </header>

        <div className="flex  justify-center items-center gap-2 sm:gap-3">
  <Link
    href="/products"
    aria-label="Browse our collection of handcrafted Chiniot furniture"
  >
    <Button
      variant="outline"
      className="bg-[#d3691e] cursor-pointer text-white px-3 sm:px-4 md:px-5 py-1.5 sm:py-2 md:py-2.5 text-[10px] sm:text-xs md:text-sm border-white"
    >
      Explore Our Collection →
    </Button>
  </Link>
  <Link
    href="/contact"
    aria-label="Contact us for custom furniture orders"
  >
    <Button
      variant="outline"
      className="border-white text-white bg-[#d3691e] cursor-pointer px-3 sm:px-4 md:px-5 py-1.5 sm:py-2 md:py-2.5 text-[10px] sm:text-xs md:text-sm"
    >
      Custom Orders
    </Button>
  </Link>
</div>


        {/* Trust indicators */}
        <div className="grid grid-cols-2 sm:flex sm:flex-wrap gap-3 sm:gap-4 md:gap-6 text-white text-xs sm:text-sm w-full justify-items-center justify-center">
          <div className="flex items-center gap-1.5 sm:gap-2">
            <span className="text-[#d3691e] text-sm sm:text-base">✓</span>
            <span className="drop-shadow-md">500+ Years Heritage</span>
          </div>
          <div className="flex items-center gap-1.5 sm:gap-2">
            <span className="text-[#d3691e] text-sm sm:text-base">✓</span>
            <span className="drop-shadow-md">Worldwide Shipping</span>
          </div>
          <div className="flex items-center gap-1.5 sm:gap-2">
            <span className="text-[#d3691e] text-sm sm:text-base">✓</span>
            <span className="drop-shadow-md">Authentic Craftsmanship</span>
          </div>
          <div className="flex items-center gap-1.5 sm:gap-2">
            <span className="text-[#d3691e] text-sm sm:text-base">✓</span>
            <span className="drop-shadow-md">Made in Chiniot, Pakistan</span>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;

import React from "react";
import { But<PERSON> } from "../components/ui/button";
import { secondaryFont } from "@/constants/fonts";
import Link from "next/link";
import Image from "next/image";

const Hero = () => {
  return (
    <section
      className="relative bg-cover bg-no-repeat bg-center w-full min-h-[32rem] sm:min-h-[36rem] md:h-[30rem] px-4 sm:px-5 py-8 sm:py-12 "
      aria-labelledby="hero-heading"
      role="banner"
    >
      {/* Background Image with proper alt text for SEO */}
      <div className="absolute inset-0 " >
        <Image
          src="/assets/backgrounds/home-bg.png"
          alt="Traditional Chinioti wooden furniture craftsman creating handcrafted furniture in Chiniot, Punjab, Pakistan workshop"
          fill
          className="object-cover"
          priority
          sizes="100vw"
        />

      </div>
          <div className="absolute inset-0 bg-black/30 z-[1]" />

      <div className="relative flex flex-col justify-center items-start h-full gap-6 sm:gap-8 md:gap-10 z-10 ">
        <header>
          <h1
            id="hero-heading"
            className={
              secondaryFont +
              " text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-semibold font-secondary text-white leading-tight drop-shadow-lg"
            }
          >
            Discover the World of{" "}
            <span className="text-accent">Authentic Chinioti</span>
            <br />
            <span className="text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl">
              Handcrafted Wooden Furniture
            </span>
          </h1>
          <p className="text-white text-md sm:text-base md:text-lg lg:text-xl mt-3 sm:mt-4 max-w-2xl drop-shadow-md">
            Premium handcrafted wooden furniture from Chiniot, Punjab, Pakistan.
            {/* 500+ years of traditional craftsmanship meets modern design.
            Authentic beds, tables, chairs, and custom furniture with worldwide shipping. */}
          </p>
        </header>

        <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 w-full sm:w-auto">
          <Link href="/products" aria-label="Browse our collection of handcrafted Chiniot furniture" className="w-full sm:w-auto">
            <Button
            variant="outline"
              className="bg-[#d3691e]  cursor-pointer text-white px-6 sm:px-8 py-2 sm:py-3 text-base sm:text-lg w-full sm:w-auto border-white"
            >
              Explore Our Collection →
            </Button>
          </Link>
          <Link href="/contact" aria-label="Contact us for custom furniture orders" className="w-full sm:w-auto">
            <Button
              variant="outline"
              className="border-white text-white bg-[#d3691e] cursor-pointer px-6 sm:px-8 py-2 sm:py-3 text-base sm:text-lg w-full sm:w-auto"
            >
              Custom Orders
            </Button>
          </Link>
        </div>

        {/* Trust indicators */}
        <div className="grid grid-cols-2 sm:flex sm:flex-wrap gap-3 sm:gap-4 md:gap-6 text-white text-xs sm:text-sm w-full">
          <div className="flex items-center gap-1.5 sm:gap-2">
            <span className="text-[#d3691e] text-sm sm:text-base">✓</span>
            <span className="drop-shadow-md">500+ Years Heritage</span>
          </div>
          <div className="flex items-center gap-1.5 sm:gap-2">
            <span className="text-[#d3691e] text-sm sm:text-base">✓</span>
            <span className="drop-shadow-md">Worldwide Shipping</span>
          </div>
          <div className="flex items-center gap-1.5 sm:gap-2">
            <span className="text-[#d3691e] text-sm sm:text-base">✓</span>
            <span className="drop-shadow-md">Authentic Craftsmanship</span>
          </div>
          <div className="flex items-center gap-1.5 sm:gap-2">
            <span className="text-[#d3691e] text-sm sm:text-base">✓</span>
            <span className="drop-shadow-md">Made in Chiniot, Pakistan</span>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;

"use client";

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { getVideoBlogs, VideoBlogData, VideoBlogPagination } from '@/apis/videoBlogs';
import VideoBlogCard from './VideoBlogCard';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Search, ChevronLeft, ChevronRight, Video, AlertCircle } from 'lucide-react';
import { toast } from 'sonner';

interface VideoBlogGridProps {
  initialPage?: number;
  itemsPerPage?: number;
  showSearch?: boolean;
  showPagination?: boolean;
  className?: string;
}

const VideoBlogGrid: React.FC<VideoBlogGridProps> = ({
  initialPage = 1,
  itemsPerPage = 12,
  showSearch = true,
  showPagination = true,
  className = ""
}) => {
  const [videoBlogs, setVideoBlogs] = useState<VideoBlogData[]>([]);
  const [pagination, setPagination] = useState<VideoBlogPagination>({
    currentPage: initialPage,
    totalPages: 0,
    totalItems: 0,
    itemsPerPage,
    hasNextPage: false,
    hasPrevPage: false
  });
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [searchInput, setSearchInput] = useState('');

  // Fetch video blogs
  const fetchVideoBlogs = async (page: number = 1, search?: string) => {
    setIsLoading(true);
    try {
      const result = await getVideoBlogs(page, itemsPerPage, search);
      setVideoBlogs(result.videoBlogs);
      setPagination(result.pagination);
    } catch (error) {
      console.error('Error fetching video blogs:', error);
      toast.error('Failed to load video blogs');
    } finally {
      setIsLoading(false);
    }
  };

  // Initial load
  useEffect(() => {
    fetchVideoBlogs(initialPage);
  }, [initialPage, itemsPerPage]);

  // Handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    const trimmedSearch = searchInput.trim();
    setSearchTerm(trimmedSearch);
    fetchVideoBlogs(1, trimmedSearch || undefined);
  };

  // Handle page change
  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= pagination.totalPages) {
      fetchVideoBlogs(newPage, searchTerm || undefined);
      // Scroll to top of grid
      document.getElementById('video-blog-grid')?.scrollIntoView({ 
        behavior: 'smooth',
        block: 'start'
      });
    }
  };

  // Clear search
  const clearSearch = () => {
    setSearchInput('');
    setSearchTerm('');
    fetchVideoBlogs(1);
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5
      }
    }
  };

  return (
    <div id="video-blog-grid" className={`w-full ${className}`}>
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center gap-3 mb-4">
          <Video className="w-8 h-8 text-primary" />
          <h2 className="text-3xl font-bold text-gray-900">Video Blogs</h2>
        </div>
        
        <p className="text-gray-600 mb-6">
          Discover our collection of video content showcasing Chinioti wooden art and craftsmanship.
        </p>

        {/* Search */}
        {showSearch && (
          <form onSubmit={handleSearch} className="flex gap-2 max-w-md">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                type="text"
                placeholder="Search video blogs..."
                value={searchInput}
                onChange={(e) => setSearchInput(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button type="submit" disabled={isLoading}>
              Search
            </Button>
            {searchTerm && (
              <Button type="button" variant="outline" onClick={clearSearch}>
                Clear
              </Button>
            )}
          </form>
        )}

        {/* Search results info */}
        {searchTerm && (
          <div className="mt-4 text-sm text-gray-600">
            {pagination.totalItems > 0 ? (
              <>Found {pagination.totalItems} video{pagination.totalItems !== 1 ? 's' : ''} for "{searchTerm}"</>
            ) : (
              <>No videos found for "{searchTerm}"</>
            )}
          </div>
        )}
      </div>

      {/* Loading state */}
      {isLoading && (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      )}

      {/* Empty state */}
      {!isLoading && videoBlogs.length === 0 && (
        <div className="text-center py-12">
          <AlertCircle className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            {searchTerm ? 'No videos found' : 'No video blogs available'}
          </h3>
          <p className="text-gray-600 mb-4">
            {searchTerm 
              ? 'Try adjusting your search terms or browse all videos.'
              : 'Check back later for new video content.'
            }
          </p>
          {searchTerm && (
            <Button onClick={clearSearch} variant="outline">
              View All Videos
            </Button>
          )}
        </div>
      )}

      {/* Video grid */}
      {!isLoading && videoBlogs.length > 0 && (
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
        >
          {videoBlogs.map((videoBlog) => (
            <motion.div key={videoBlog.id} variants={itemVariants}>
              <VideoBlogCard videoBlog={videoBlog} />
            </motion.div>
          ))}
        </motion.div>
      )}

      {/* Pagination */}
      {showPagination && !isLoading && pagination.totalPages > 1 && (
        <div className="mt-12 flex items-center justify-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(pagination.currentPage - 1)}
            disabled={!pagination.hasPrevPage}
          >
            <ChevronLeft className="w-4 h-4" />
            Previous
          </Button>

          <div className="flex items-center gap-1">
            {/* Page numbers */}
            {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
              let pageNumber;
              if (pagination.totalPages <= 5) {
                pageNumber = i + 1;
              } else if (pagination.currentPage <= 3) {
                pageNumber = i + 1;
              } else if (pagination.currentPage >= pagination.totalPages - 2) {
                pageNumber = pagination.totalPages - 4 + i;
              } else {
                pageNumber = pagination.currentPage - 2 + i;
              }

              return (
                <Button
                  key={pageNumber}
                  variant={pageNumber === pagination.currentPage ? "default" : "outline"}
                  size="sm"
                  onClick={() => handlePageChange(pageNumber)}
                  className="w-10"
                >
                  {pageNumber}
                </Button>
              );
            })}
          </div>

          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(pagination.currentPage + 1)}
            disabled={!pagination.hasNextPage}
          >
            Next
            <ChevronRight className="w-4 h-4" />
          </Button>
        </div>
      )}

      {/* Pagination info */}
      {showPagination && !isLoading && videoBlogs.length > 0 && (
        <div className="mt-4 text-center text-sm text-gray-600">
          Showing {((pagination.currentPage - 1) * pagination.itemsPerPage) + 1} to{' '}
          {Math.min(pagination.currentPage * pagination.itemsPerPage, pagination.totalItems)} of{' '}
          {pagination.totalItems} video{pagination.totalItems !== 1 ? 's' : ''}
        </div>
      )}
    </div>
  );
};

export default VideoBlogGrid;
